<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket Status Display Example</title>
    <link rel="stylesheet" href="src/socket-status.css">
</head>
<body>
    <h1>CxOne Socket Status Display Example</h1>
    
    <div style="margin: 20px 0;">
        <h2>Socket Status Display:</h2>
        <div id="cxone-socket-status">
            <!-- Socket status will be displayed here -->
            <div class="socket-status-container socket-status--disconnected">
                <div class="socket-status-header">
                    <span class="socket-status-icon">○</span>
                    <span class="socket-status-text">Disconnected</span>
                </div>
                <div class="socket-status-details">
                    <div class="socket-status-row">
                        <span class="socket-status-label">Connection ID:</span>
                        <span class="socket-status-value">N/A</span>
                    </div>
                    <div class="socket-status-row">
                        <span class="socket-status-label">Hub URL:</span>
                        <span class="socket-status-value">N/A</span>
                    </div>
                    <div class="socket-status-row">
                        <span class="socket-status-label">Connected Since:</span>
                        <span class="socket-status-value">N/A</span>
                    </div>
                    <div class="socket-status-row">
                        <span class="socket-status-label">Uptime:</span>
                        <span class="socket-status-value">N/A</span>
                    </div>
                    <div class="socket-status-row">
                        <span class="socket-status-label">Last Heartbeat:</span>
                        <span class="socket-status-value">N/A</span>
                    </div>
                    <div class="socket-status-row">
                        <span class="socket-status-label">Reconnect Attempts:</span>
                        <span class="socket-status-value">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="margin: 20px 0;">
        <h2>Usage Instructions:</h2>
        <p>To use the socket status display functions in your CxOne application:</p>
        
        <h3>1. Include the CSS:</h3>
        <pre><code>&lt;link rel="stylesheet" href="src/socket-status.css"&gt;</code></pre>
        
        <h3>2. Add the HTML element:</h3>
        <pre><code>&lt;div id="cxone-socket-status"&gt;&lt;/div&gt;</code></pre>
        
        <h3>3. Use the JavaScript functions:</h3>
        <pre><code>// Get CxOne instance
var cxOne = getCxOneInstance();

// Build and display socket status template
var template = cxOne.buildSocketStatusTemplate();
document.getElementById('cxone-socket-status').innerHTML = template;

// Or use the update function directly
cxOne.updateSocketStatusDisplay();

// The display will automatically update when socket status changes</code></pre>
        
        <h3>4. Available Functions:</h3>
        <ul>
            <li><strong>buildSocketStatusTemplate()</strong> - Returns HTML template string for current socket status</li>
            <li><strong>updateSocketStatusDisplay()</strong> - Updates the DOM element with current socket status</li>
            <li><strong>getSignalRStatus()</strong> - Returns current socket status object</li>
        </ul>
        
        <h3>5. Status States:</h3>
        <ul>
            <li><strong>Connected</strong> - Green indicator, shows connection details</li>
            <li><strong>Connecting</strong> - Orange pulsing indicator</li>
            <li><strong>Disconnected</strong> - Red indicator</li>
        </ul>
    </div>

    <script>
        // Example of how the functions would be called
        // (This is just for demonstration - actual implementation would use the CxOne instance)
        console.log('Socket status display functions are available in the CxOne instance');
        console.log('Use: var cxOne = getCxOneInstance(); cxOne.updateSocketStatusDisplay();');
    </script>
</body>
</html>
