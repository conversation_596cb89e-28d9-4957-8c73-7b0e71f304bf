/* Socket Status Display Styles */
.socket-status-container {
    font-family: Arial, sans-serif;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    background-color: #f9f9f9;
    max-width: 300px;
}

.socket-status-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: bold;
}

.socket-status-icon {
    margin-right: 6px;
    font-size: 14px;
}

.socket-status-text {
    font-size: 13px;
}

.socket-status-details {
    border-top: 1px solid #eee;
    padding-top: 6px;
}

.socket-status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    padding: 2px 0;
}

.socket-status-label {
    font-weight: 500;
    color: #666;
    flex: 0 0 auto;
    margin-right: 8px;
}

.socket-status-value {
    color: #333;
    flex: 1;
    text-align: right;
    word-break: break-all;
    font-family: monospace;
    font-size: 11px;
}

/* Status-specific styling */
.socket-status--connected {
    border-color: #4CAF50;
    background-color: #f1f8e9;
}

.socket-status--connected .socket-status-icon {
    color: #4CAF50;
}

.socket-status--connected .socket-status-text {
    color: #2E7D32;
}

.socket-status--connecting {
    border-color: #FF9800;
    background-color: #fff8e1;
}

.socket-status--connecting .socket-status-icon {
    color: #FF9800;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

.socket-status--connecting .socket-status-text {
    color: #E65100;
}

.socket-status--disconnected {
    border-color: #f44336;
    background-color: #ffebee;
}

.socket-status--disconnected .socket-status-icon {
    color: #f44336;
}

.socket-status--disconnected .socket-status-text {
    color: #c62828;
}

/* Animation for connecting state */
@keyframes pulse {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.4;
    }
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .socket-status-container {
        max-width: 100%;
        font-size: 11px;
    }
    
    .socket-status-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .socket-status-value {
        text-align: left;
        margin-top: 2px;
    }
}
